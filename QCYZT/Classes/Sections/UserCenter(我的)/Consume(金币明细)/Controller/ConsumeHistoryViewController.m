//
//  ConsumeHistoryViewController.m
//  QCYZT
//
//  Created by leo on 16/9/1.
//  Copyright © 2016年 sdcf. All rights reserved.
//

#import "ConsumeHistoryViewController.h"
#import "YTGOtherWebVC.h"
#import "FMPayTool.h"

#define kHeaderHeight (UI_Relative_WidthValue(120) + UI_SAFEAREA_TOP_HEIGHT)

@interface ConsumeHistoryViewController () <SGPageTitleViewDelegate,SGPageContentCollectionViewDelegate>

@property (nonatomic, strong) UIImageView *topImgV;
@property (nonatomic, strong) UIView *coinView;
@property (nonatomic,weak) UILabel *coinVauleLabel;
@property (nonatomic, strong) UIStackView *pointsView;
@property (nonatomic, strong) UILabel *pointsLabel;
@property (nonatomic, strong) UILabel *pointsValueLabel;
@property (nonatomic, strong) UIView *expireView;
@property (nonatomic, strong) UILabel *expireLabel;

@property (nonatomic, strong) NSArray *titleArr;
@property (nonatomic, strong) SGPageTitleView *pageTitleView;
@property (nonatomic, strong) SGPageContentCollectionView *pageContentCollectionView;



@end

@implementation ConsumeHistoryViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = UIColor.up_contentBgColor;

    [self.view addSubview:self.topImgV];
    [self.topImgV mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.left.right.equalTo(0);
        make.height.equalTo(UI_Relative_WidthValue(137) + UI_SAFEAREA_TOP_HEIGHT);
    }];
    
    self.titleArr = @[@{@"title":@"支出", @"vc":@"ConsumeExpandViewController"},
                      @{@"title":@"收入", @"vc":@"ConsumeIncomeViewController"}];
    

    [self setupPageView];
}

- (void)viewWillAppear:(BOOL)animated {
    self.selfNavigationBarHidden = YES;
    [super viewWillAppear:animated];
    
    [HttpRequestTool queryDakaCoinsAndPointsStart:nil failure:nil success:^(NSDictionary *dic) {
        FMUserModel *userModel = [MyKeyChainManager load:kUserModel];
        self.coinVauleLabel.text = [NSString stringWithFormat:@"%zd", userModel.coin];
        self.pointsValueLabel.text = [NSString stringWithFormat:@"%zd", userModel.points];
        
        NSString *expirePoints = [NSString stringWithFormat:@"%@", dic[@"data"][@"expirePoints"]];
        if (expirePoints.integerValue > 0) {
            long long timestamp = [dic[@"data"][@"expireTime"] longLongValue];
            NSDate *expireDate = [NSDate dateWithTimeIntervalSince1970:timestamp / 1000];
            NSString *expireDateStr = [NSString stringWithFormat:@"其中%@积分将于%@过期", expirePoints, [expireDate dateStringWithFormatString:@"yyyy/MM/dd"]];
            NSMutableAttributedString *expireAttrStr = [[NSMutableAttributedString alloc] initWithString:expireDateStr];
            [expireAttrStr addAttribute:NSForegroundColorAttributeName value:FMNavColor range:NSMakeRange(2, expirePoints.length)];
            self.expireLabel.attributedText = expireAttrStr;
            
            self.expireView.hidden = NO;
            [self.pointsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(UI_Relative_WidthValue(12.5));
            }];
            [self.pointsValueLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(UI_Relative_WidthValue(-5));
            }];
        } else {
            self.expireView.hidden = YES;
            [self.pointsLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.top.equalTo(UI_Relative_WidthValue(20));
            }];
            [self.pointsValueLabel mas_updateConstraints:^(MASConstraintMaker *make) {
                make.bottom.equalTo(UI_Relative_WidthValue(-20));
            }];
        }
    }];
}

#pragma mark - SGPageTitleViewDelegate
- (void)pageTitleView:(SGPageTitleView *)pageTitleView selectedIndex:(NSInteger)selectedIndex{
    [self.pageContentCollectionView setPageContentCollectionViewCurrentIndex:selectedIndex];
}

#pragma mark - SGPageContentCollectionViewDelegate
- (void)pageContentCollectionView:(SGPageContentCollectionView *)pageContentCollectionView progress:(CGFloat)progress originalIndex:(NSInteger)originalIndex targetIndex:(NSInteger)targetIndex {
    [self.pageTitleView setPageTitleViewWithProgress:progress originalIndex:originalIndex targetIndex:targetIndex];
}

#pragma mark - Private
-(void)setupPageView {
    [self.pageTitleView removeFromSuperview];
    [self.pageContentCollectionView removeFromSuperview];
    self.pageTitleView = nil;
    self.pageContentCollectionView = nil;

    SGPageTitleViewConfigure *configure = [SGPageTitleViewConfigure pageTitleViewConfigure];
    configure.titleColor = UIColor.up_textSecondaryColor;
    configure.titleFont = FontWithSize(17.0);
    configure.titleSelectedColor = UIColor.up_textPrimaryColor;
    configure.titleSelectedFont = BoldFontWithSize(17.0);
    configure.indicatorStyle = SGIndicatorStyleFixed;
    configure.indicatorColor = FMNavColor;
    configure.indicatorFixedWidth = 18;
    configure.indicatorHeight = 3;
    configure.indicatorCornerRadius = 1.5;
    configure.titleAdditionalWidth = 30;
    configure.equivalence = YES;
    configure.showBottomSeparator = YES;
    configure.bottomSeparatorColor = UIColor.fm_sepline_color;
    self.pageTitleView = [SGPageTitleView pageTitleViewWithFrame:CGRectMake(0, kHeaderHeight, UI_SCREEN_WIDTH, 45) delegate:self titleNames:[self.titleArr valueForKeyPath:@"title"] configure:configure];
    self.pageTitleView.backgroundColor = UIColor.up_contentBgColor;
    [self.pageTitleView layerAndBezierPathWithRect:CGRectMake(0, 0, UI_SCREEN_WIDTH, 45) cornerRadii:CGSizeMake(10, 10) byRoundingCorners:UIRectCornerTopLeft|UIRectCornerTopRight];
    [self.view addSubview:self.pageTitleView];
    
    self.pageContentCollectionView = [[SGPageContentCollectionView alloc] initWithFrame:CGRectMake(0, kHeaderHeight + 45, UI_SCREEN_WIDTH, UI_SCREEN_HEIGHT-(45 + kHeaderHeight + UI_SAFEAREA_BOTTOM_HEIGHT)) parentVC:self childVCs:[self addChildVC]];
    self.pageContentCollectionView.delegatePageContentCollectionView = self;
    [self.view addSubview:self.pageContentCollectionView];
    // 处理侧滑返回失效
    [self.pageContentCollectionView.collectionView.panGestureRecognizer requireGestureRecognizerToFail:self.navigationController.interactivePopGestureRecognizer];
    
    self.pageTitleView.selectedIndex = self.index;
}

- (NSArray *)addChildVC {
    [self.titleArr enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
        Class clazz = NSClassFromString([_titleArr[idx] objectForKey:@"vc"]);
        UIViewController * vc = [[clazz alloc] init];
        
        [self addChildViewController:vc];
    }];
    
    return self.childViewControllers;
}

- (void)explainBtnClick {
    WEAKSELF
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        // 金币说明
        YTGOtherWebVC *vc = [[YTGOtherWebVC alloc] init];
        vc.startPage = [NSString stringWithFormat:@"%@%@",prefix,kAPI_UserCenter_TGBSM];;
        vc.titleStr = @"金币说明";
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    }];
}

- (void)explainBtnClick2 {
    WEAKSELF
    [FMUserDefault getPrefixWithType:AppInit_contentPrefix getPrefixBlock:^(NSString *prefix) {
        // 积分说明
        YTGOtherWebVC *vc = [[YTGOtherWebVC alloc] init];
        vc.startPage = [NSString stringWithFormat:@"%@%@",prefix, kAPI_UserCenter_TGBSM]; 
        vc.titleStr = @"积分说明";
        [__weakSelf.navigationController pushViewController:vc animated:YES];
    }];
}

- (void)jumpToRecharge {
    [[FMPayTool payTool] gotoRecharge];
}

- (void)earnPoints {

}

- (void)backArrowClicked {
    [self.navigationController popViewControllerAnimated:YES];
}

- (UIImageView *)topImgV {
    if (!_topImgV) {
        _topImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"consume_top")];
        _topImgV.userInteractionEnabled = YES;
        
        UIButton *backArrow = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"return") target:self action:@selector(backArrowClicked)];
        [_topImgV addSubview:backArrow];
        [backArrow mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@(UI_STATUS_HEIGHT));
            make.height.width.equalTo(UI_NAVBAR_HEIGHT);
            make.left.equalTo(@0);
        }];
        
        UILabel *label = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(18) textColor:FMWhiteColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [_topImgV addSubview:label];
        [label mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(0);
            make.centerY.equalTo(backArrow);
        }];
        label.text = @"收支明细";
        
        [_topImgV addSubview:self.coinView];
        [self.coinView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.top.equalTo(backArrow.mas_bottom).offset(UI_Relative_WidthValue(15));
            make.height.equalTo(UI_Relative_WidthValue(90));
            make.width.equalTo((UI_SCREEN_WIDTH - 40) * 0.42);
        }];
        
        [_topImgV addSubview:self.pointsView];
        [self.pointsView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-15);
            make.top.equalTo(self.coinView);
            make.height.equalTo(UI_Relative_WidthValue(90));
            make.width.equalTo((UI_SCREEN_WIDTH - 40) * 0.58);
        }];
    }
    
    return _topImgV;
}

- (UIView *)coinView {
    if (!_coinView) {
        _coinView = [UIView new];
        UI_View_Radius(_coinView, 10);
        _coinView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xfff1d3), FMWhiteColor] withFrame:CGRectMake(0, 0, (UI_SCREEN_WIDTH - 40) * 0.42, UI_Relative_WidthValue(90)) direction:GradientDirectionLeftToRight];
        
        UILabel *coinLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        coinLabel.text = @"金币余额";
        [_coinView addSubview:coinLabel];
        [coinLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.top.equalTo(UI_Relative_WidthValue(20));
        }];
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"consume_wenhao") target:self action:@selector(explainBtnClick)];
        [_coinView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(coinLabel.mas_right).offset(3);
            make.centerY.equalTo(coinLabel);
        }];
        btn.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
        
        UILabel *coinValueLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(20) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [_coinView addSubview:coinValueLabel];
        [coinValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(coinLabel);
            make.bottom.equalTo(UI_Relative_WidthValue(-20));
        }];
        self.coinVauleLabel = coinValueLabel;
        
        UIButton *arrowBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:ColorWithHex(0xfc3b3d) backgroundColor:FMClearColor title:@"充值" image:ImageWithName(@"consume_arrow") target:self action:@selector(jumpToRecharge)];
        [_coinView addSubview:arrowBtn];
        [arrowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-15);
            make.centerY.equalTo(coinValueLabel);
        }];
        [arrowBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:3];
    }
    
    return _coinView;
}

- (UIView *)pointsView {
    if (!_pointsView) {
        _pointsView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisVertical alignment:UIStackViewAlignmentFill distribution:UIStackViewDistributionFill spacing:0 arrangedSubviews:nil];
        UI_View_Radius(_pointsView, 10);

        UIView *topView = [UIView new];
        [_pointsView addArrangedSubview:topView];
        topView.backgroundColor = [UIColor lz_gradientColors:@[ColorWithHex(0xffdddd), FMWhiteColor] withFrame:CGRectMake(0, 0, (UI_SCREEN_WIDTH - 40) * 0.58, UI_Relative_WidthValue(67)) direction:GradientDirectionLeftToRight];
        
        UILabel *pointsLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x666666) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        pointsLabel.text = @"积分余额";
        [topView addSubview:pointsLabel];
        [pointsLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.top.equalTo(UI_Relative_WidthValue(12.5));
        }];
        self.pointsLabel = pointsLabel;
        
        UIButton *btn = [[UIButton alloc] initWithFrame:CGRectZero font:nil normalTextColor:nil backgroundColor:FMClearColor title:nil image:ImageWithName(@"consume_wenhao") target:self action:@selector(explainBtnClick2)];
        [topView addSubview:btn];
        [btn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(pointsLabel.mas_right).offset(3);
            make.centerY.equalTo(pointsLabel);
        }];
        btn.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
        
        UILabel *pointsValueLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(20) textColor:ColorWithHex(0x333333) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
        [topView addSubview:pointsValueLabel];
        [pointsValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(pointsLabel);
            make.bottom.equalTo(UI_Relative_WidthValue(-5));
        }];
        self.pointsValueLabel = pointsValueLabel;
        
        UIButton *arrowBtn = [[UIButton alloc] initWithFrame:CGRectZero font:FontWithSize(12) normalTextColor:ColorWithHex(0xfc3b3d) backgroundColor:FMClearColor title:@"获得积分" image:ImageWithName(@"consume_arrow") target:self action:@selector(earnPoints)];
        [topView addSubview:arrowBtn];
        [arrowBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(-15);
            make.centerY.equalTo(pointsValueLabel);
        }];
        [arrowBtn layoutButtonWithEdgInsetsStyle:ButtonEdgeInsetsStyleImageRight imageTitleSpacing:3];
        
        UIView *expireView = [UIView new];
        expireView.backgroundColor = ColorWithHex(0xffeded);
        [_pointsView addArrangedSubview:expireView];
        [expireView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(UI_Relative_WidthValue(23));
        }];
        self.expireView = expireView;
        
        UILabel *expireLabel = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:ColorWithHex(0x846565) backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        [expireView addSubview:expireLabel];
        [expireLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.center.equalTo(0);
            make.left.equalTo(10);
        }];
        expireLabel.adjustsFontSizeToFitWidth = YES;
        self.expireLabel = expireLabel;
    }
    
    return _pointsView;
}


@end
