//
//  PayPointsCell.m
//  QCYZT
//
//  Created by Augment on 2025-07-30.
//  Copyright © 2025年 sdcf. All rights reserved.
//

#import "PayPointsCell.h"
#import "FMProgressHUD.h"

typedef NS_ENUM(NSUInteger, PointsShowType) {
    PointsShowTypeDisable = -1,
    PointsShowTypeZero = 0,
    PointsShowTypeEnable
};



@interface PayPointsCell ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *helpButton;
@property (nonatomic, strong) UIStackView *rightStackView;
@property (nonatomic, strong) UILabel *numLabel;
@property (nonatomic, strong) YYLabel *descLabel;
@property (nonatomic, strong) UIButton *checkboxButton;

@property (nonatomic, assign) NSInteger userPoints;
@property (nonatomic, assign) NSInteger orderAmount;
@property (nonatomic, assign) NSInteger couponDiscount;
@property (nonatomic, assign) NSInteger pointsRatio;
@property (nonatomic, assign) BOOL isSelected;
@property (nonatomic, copy) PointsSelectionChangedBlock selectionChangedBlock;

@end

@implementation PayPointsCell


- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    self.backgroundColor = UIColor.up_contentBgColor;
    self.selectionStyle = UITableViewCellSelectionStyleNone;

    // 1. 左侧标题
    self.titleLabel = [[UILabel alloc] initWithFrame:CGRectZero
                                                font:FontWithSize(16)
                                           textColor:UIColor.up_textSecondaryColor
                                     backgroundColor:FMClearColor
                                       numberOfLines:1
                                       textAlignment:NSTextAlignmentLeft];
    self.titleLabel.text = @"积分";
    [self.contentView addSubview:self.titleLabel];

    // 2. 问号按钮
    self.helpButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.helpButton setImage:[UIImage imageNamed:@"pay_wenhao"] forState:UIControlStateNormal];
    [self.helpButton addTarget:self action:@selector(helpButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.helpButton];

    // 3. 右侧StackView
    self.rightStackView = [[UIStackView alloc] initWithAxis:UILayoutConstraintAxisHorizontal alignment:UIStackViewAlignmentCenter distribution:UIStackViewDistributionEqualSpacing spacing:8 arrangedSubviews:nil];
    [self.contentView addSubview:self.rightStackView];

    // 3.1 用户积分数量
    self.numLabel = [[UILabel alloc] init];
    self.numLabel.font = FontWithSize(16);
    self.numLabel.textColor = UIColor.up_textPrimaryColor;
    [self.rightStackView addArrangedSubview:self.numLabel];

    // 3.2 描述标签
    self.descLabel = [[YYLabel alloc] init];
    self.descLabel.font = FontWithSize(14);
    self.descLabel.textAlignment = NSTextAlignmentLeft;
    [self.rightStackView addArrangedSubview:self.descLabel];

    // 3.3 勾选框按钮
    self.checkboxButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.checkboxButton addTarget:self action:@selector(checkboxButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.rightStackView addArrangedSubview:self.checkboxButton];

    // 添加整体点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(handleTap)];
    [self.contentView addGestureRecognizer:tapGesture];

    // 分割线
    [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.right.bottom.equalTo(0);
        make.height.equalTo(0.5);
    }].backgroundColor = UIColor.fm_sepline_color;

    [self setupConstraints];
}

- (void)setupConstraints {
    // 标题标签
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.centerY.equalTo(0);
    }];

    // 问号按钮
    [self.helpButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.titleLabel.mas_right).offset(5);
        make.centerY.equalTo(self.titleLabel);
        make.width.height.equalTo(@16);
    }];

    // 右侧StackView
    [self.rightStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(0);
        // 移除左侧约束，让StackView根据内容自然确定宽度
    }];

    // 勾选框按钮尺寸
    [self.checkboxButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.width.height.equalTo(@20);
    }];
}

- (void)configureWithUserPoints:(NSInteger)userPoints
                    orderAmount:(NSInteger)orderAmount
                  couponDiscount:(NSInteger)couponDiscount
                    pointsRatio:(NSInteger)pointsRatio
           selectionChangedBlock:(PointsSelectionChangedBlock)selectionChangedBlock {
    self.userPoints = userPoints;
    self.orderAmount = orderAmount;
    self.couponDiscount = couponDiscount;
    self.pointsRatio = pointsRatio;
    self.isSelected = NO; // 默认未选中
    self.selectionChangedBlock = selectionChangedBlock;

    [self updateDisplay];
}

// 更新订单和卡券消耗
- (void)updateOrderAmount:(NSInteger)orderAmount couponDiscount:(NSInteger)couponDiscount {
    self.orderAmount = orderAmount;
    self.couponDiscount = couponDiscount;

    // 如果当前选中状态下积分不够用了或者不需要使用积分，自动取消选中
    if (self.isSelected && [self isPointsAvailable] != PointsShowTypeEnable) {
        self.isSelected = NO;
    }
    if (self.selectionChangedBlock) {
        self.selectionChangedBlock([self getCurrentUsePoints]);
    }

    [self updateDisplay];
}

/// 计算可抵扣的金币数量
- (NSInteger)calculateDeductibleCoins {
    NSInteger availablePoints = self.userPoints; // 可用金币
    NSInteger maxDeductibleCoins = availablePoints / self.pointsRatio; // 最大抵扣金币
    NSInteger actualOrderAmount = self.orderAmount - self.couponDiscount; // 实际订单金额
    return MIN(maxDeductibleCoins, actualOrderAmount);
}

/// 积分展示状态
- (PointsShowType)isPointsAvailable {
    // 用户积分不够
    if (self.userPoints < self.pointsRatio) {
        return PointsShowTypeDisable;
    }
    
    // 抵扣券覆盖了订单
    if (self.orderAmount <= self.couponDiscount) {
        return PointsShowTypeZero;
    }
    
    return PointsShowTypeEnable;
}

// 勾选后抵扣的积分
- (NSInteger)getCurrentUsePoints {
    if (!self.isSelected) {
        return 0;
    }

    return [self calculateDeductibleCoins] * self.pointsRatio;
}

- (void)updateDisplay {
    // 设置积分数量显示
    self.numLabel.text = [NSString stringWithFormat:@"%ld", (long)self.userPoints];

    PointsShowType status = [self isPointsAvailable];
    if (status == PointsShowTypeDisable) {
        // 状态1：积分不够 - "暂无可用"
        self.descLabel.text = @"暂无可用";
        self.descLabel.textColor = UIColor.up_textSecondary2Color;
        self.checkboxButton.hidden = YES;
        self.contentView.userInteractionEnabled = NO;
    } else if (status == PointsShowTypeZero) {
        self.checkboxButton.hidden = YES;
        self.contentView.userInteractionEnabled = NO;
        NSInteger deductibleCoins = [self calculateDeductibleCoins];
        [self setupUnselectedDescLabel:deductibleCoins];
    } else {
        NSInteger deductibleCoins = [self calculateDeductibleCoins];
        self.contentView.userInteractionEnabled = YES;
        self.checkboxButton.hidden = NO;

        if (self.isSelected) {
            // 状态3：勾选时的样式 - "-**金币"，文本全部标红
            [self setupSelectedDescLabel:deductibleCoins];
            [self.checkboxButton setImage:[UIImage imageNamed:@"paytype_select"] forState:UIControlStateNormal];
        } else {
            // 状态2：有可用积分但未勾选 - "此单可抵扣**金币，去使用>"
            [self setupUnselectedDescLabel:deductibleCoins];
            [self.checkboxButton setImage:[UIImage imageNamed:@"unchecked"] forState:UIControlStateNormal];
        }
    }
}

- (void)setupSelectedDescLabel:(NSInteger)deductibleCoins {
    NSString *text = [NSString stringWithFormat:@"-%ld金币", (long)deductibleCoins];
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:text];
    attributedText.yy_color = UIColor.up_riseColor;
    attributedText.yy_font = FontWithSize(16);
    self.descLabel.attributedText = attributedText;
}

- (void)setupUnselectedDescLabel:(NSInteger)deductibleCoins {
    NSString *text;
    if (deductibleCoins == 0) {
        text = @"此单可抵扣0金币";
    } else {
        text = [NSString stringWithFormat:@"此单可抵扣%ld金币，去使用 ", (long)deductibleCoins];
    }
    
    NSMutableAttributedString *attributedText = [[NSMutableAttributedString alloc] initWithString:text];

    // 设置默认颜色
    attributedText.yy_color = UIColor.up_textSecondaryColor;
    attributedText.yy_font = FontWithSize(16);

    // 数字+金币标红
    NSString *numberText = [NSString stringWithFormat:@"%ld金币", (long)deductibleCoins];
    NSRange numberRange = [text rangeOfString:numberText];
    if (numberRange.location != NSNotFound) {
        [attributedText yy_setColor:UIColor.up_riseColor range:numberRange];
    }
    
    if (deductibleCoins == 0) {
        self.descLabel.attributedText = attributedText;
        return;
    }

    // 添加箭头图片
    UIImage *arrowImage = [UIImage imageNamed:@"pay_arrow"];
    if (arrowImage) {
        NSMutableAttributedString *arrowAttachment = [NSMutableAttributedString yy_attachmentStringWithContent:arrowImage contentMode:UIViewContentModeCenter attachmentSize:arrowImage.size alignToFont:FontWithSize(16) alignment:YYTextVerticalAlignmentCenter];
        [attributedText appendAttributedString:arrowAttachment];
    }

    self.descLabel.attributedText = attributedText;
}

- (void)handleTap {
    if ([self isPointsAvailable] != PointsShowTypeEnable) {
        return;
    }

    // 切换选中状态
    self.isSelected = !self.isSelected;
    [self updateDisplay];

    // 通知外部状态变化
    if (self.selectionChangedBlock) {
        self.selectionChangedBlock([self getCurrentUsePoints]);
    }
}

- (void)checkboxButtonTapped {
    [self handleTap];
}

- (void)helpButtonTapped {
    NSString *str = [NSString stringWithFormat:@"积分仅用于抵扣金币，%zd积分=1金币，可在“我的-福利中心”获得", self.pointsRatio];
    [FMProgressHUD showTextOnlyInView:nil withText:str];
}




@end
